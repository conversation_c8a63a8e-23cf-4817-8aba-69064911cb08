@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Minimalistic Black & White Palette */
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;

    --card: 0 0% 98%;
    --card-foreground: 0 0% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;

    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 0 0% 20%;

    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 15%;

    --muted: 0 0% 94%;
    --muted-foreground: 0 0% 40%;

    --accent: 0 0% 85%;
    --accent-foreground: 0 0% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 0 0% 0%;

    /* Elegant Design Tokens */
    --gradient-romantic: linear-gradient(135deg, hsl(0 0% 100%), hsl(0 0% 98%), hsl(0 0% 96%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 98%), hsl(0 0% 95%));
    --gradient-primary: linear-gradient(135deg, hsl(0 0% 0%), hsl(0 0% 20%));
    --gradient-accent: linear-gradient(135deg, hsl(0 0% 85%), hsl(0 0% 80%));
    
    /* Elegant Shadows */
    --shadow-romantic: 0 20px 60px -15px hsl(0 0% 0% / 0.15);
    --shadow-soft: 0 10px 30px -10px hsl(0 0% 0% / 0.08);
    --shadow-glow: 0 0 40px hsl(0 0% 0% / 0.1);
    
    /* Smooth Animations */
    --transition-romantic: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 1.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 0 0% 20%;
    --sidebar-primary: 0 0% 0%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 95%;
    --sidebar-accent-foreground: 0 0% 15%;
    --sidebar-border: 0 0% 90%;
    --sidebar-ring: 0 0% 0%;
  }

  .dark {
    /* Elegant Dark Minimalistic Theme */
    --background: 0 0% 5%;
    --foreground: 0 0% 95%;

    --card: 0 0% 8%;
    --card-foreground: 0 0% 95%;

    --popover: 0 0% 5%;
    --popover-foreground: 0 0% 95%;

    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;
    --primary-glow: 0 0% 80%;

    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 95%;

    --muted: 0 0% 12%;
    --muted-foreground: 0 0% 60%;

    --accent: 0 0% 20%;
    --accent-foreground: 0 0% 95%;

    --destructive: 0 62.8% 50%;
    --destructive-foreground: 0 0% 95%;

    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 0 0% 100%;

    /* Dark Elegant Gradients */
    --gradient-romantic: linear-gradient(135deg, hsl(0 0% 5%), hsl(0 0% 8%), hsl(0 0% 12%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 8%), hsl(0 0% 5%));
    --gradient-primary: linear-gradient(135deg, hsl(0 0% 100%), hsl(0 0% 80%));
    --gradient-accent: linear-gradient(135deg, hsl(0 0% 20%), hsl(0 0% 15%));
    
    --shadow-romantic: 0 20px 60px -15px hsl(0 0% 100% / 0.1);
    --shadow-soft: 0 10px 30px -10px hsl(0 0% 100% / 0.05);
    --shadow-glow: 0 0 40px hsl(0 0% 100% / 0.08);

    --sidebar-background: 0 0% 8%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 0 0% 15%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 0 0% 15%;
    --sidebar-ring: 0 0% 100%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
  
  /* 3D Transform Utilities */
  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-style-preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }
}