import { LetterSection } from "@/components/LetterSection";
import { VideoDisplay } from "@/components/VideoDisplay";
import { PictureDisplay } from "@/components/PictureDisplay";
import { FlashcardSection } from "@/components/FlashcardSection";
import heroBackground from "@/assets/hero-background.jpg";

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center">
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30"
          style={{ backgroundImage: `url(${heroBackground})` }}
        />
        <div className="relative z-10 text-center px-4 max-w-2xl mx-auto">
          <LetterSection>
            <p className="text-sm uppercase tracking-widest text-muted-foreground mb-8 font-sans">
              For My Love
            </p>
            <h1 className="text-5xl md:text-7xl font-light text-foreground mb-8 leading-tight">
              Black & White
            </h1>
            <p className="text-lg text-muted-foreground font-light leading-relaxed">
              It could be so simple — just you and me,<br />
              in perfect clarity
            </p>
          </LetterSection>
        </div>
      </section>

      {/* Letter Content */}
      <section className="max-w-4xl mx-auto px-6 py-20 space-y-16">
        
        <LetterSection delay={200}>
          <div className="prose prose-lg max-w-none">
            <p className="text-xl text-foreground font-light leading-relaxed mb-8">
              My Dearest,
            </p>
            <p className="text-lg text-muted-foreground font-light leading-relaxed">
              Let me in your temple. Show me what you're into. I want to know every corner 
              of your heart, every dream that lives behind your eyes. You come first, 
              like rent due — a priority that shapes everything else in my world.
            </p>
          </div>
        </LetterSection>

        <LetterSection delay={400}>
          <div className="border-l-2 border-foreground/20 pl-8">
            <p className="text-2xl text-foreground font-light leading-relaxed italic">
              "I see our potential"
            </p>
            <p className="text-lg text-muted-foreground font-light leading-relaxed mt-4">
              What we have doesn't need complications. No games, no pretense — 
              just this beautiful thing between us, as clear and honest as black and white.
            </p>
          </div>
        </LetterSection>

        <LetterSection delay={600}>
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-light text-foreground mb-6">
                My Promises
              </h2>
              <div className="space-y-6 text-muted-foreground">
                <p className="font-light leading-relaxed">
                  <span className="text-foreground font-medium">One:</span> Never give up on you, or me, or us.
                </p>
                <p className="font-light leading-relaxed">
                  <span className="text-foreground font-medium">Two:</span> Be patient with you like you're patient with me.
                </p>
                <p className="font-light leading-relaxed">
                  <span className="text-foreground font-medium">Three:</span> Help you see through those who pretend.
                </p>
                <p className="font-light leading-relaxed">
                  <span className="text-foreground font-medium">Four:</span> You already know — you're the one.
                </p>
              </div>
            </div>
            <div className="text-center">
              <div className="w-32 h-32 mx-auto border border-foreground/20 rounded-full flex items-center justify-center">
                <span className="text-4xl">∞</span>
              </div>
              <p className="text-sm text-muted-foreground mt-4 font-light">
                We the perfect two
              </p>
            </div>
          </div>
        </LetterSection>

        <LetterSection delay={800}>
          <div className="text-center py-12">
            <p className="text-3xl text-foreground font-light leading-relaxed">
              "I'll be your peace,<br />
              you be my baby"
            </p>
            <div className="w-16 h-px bg-foreground/30 mx-auto mt-8"></div>
          </div>
        </LetterSection>

        <LetterSection delay={1000}>
          <div className="bg-card rounded-lg p-8 border border-border/20">
            <p className="text-lg text-muted-foreground font-light leading-relaxed">
              I got a lot of love to give. I want you to call dibs on this heart 
              that beats for you. Can you hear it? Like any home, it comes with 
              promises — to never disappear, to be your bridge when you need to 
              cross over into something new.
            </p>
            <p className="text-lg text-foreground font-light leading-relaxed mt-6">
              You make heaven make sense.
            </p>
          </div>
        </LetterSection>

        <FlashcardSection />

        <LetterSection delay={1200}>
          <div>
            <h2 className="text-3xl font-light text-foreground mb-8 text-center">
              My Favorite Pictures of You
            </h2>
            <p className="text-lg text-muted-foreground font-light leading-relaxed text-center mb-12">
              Every angle, every smile, every moment — you're breathtaking
            </p>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <PictureDisplay
                title="Your beautiful heart"
                description="The way you light up every room you enter"
                imageSrc="/images/IMG_0087.jpeg"
              />
              <PictureDisplay
                title="The blissful radiance you emit"
                description="When you catch me staring and smile back"
                imageSrc="/images/IMG_0666.jpeg"
              />
              <PictureDisplay
                title="Your natural beauty"
                description="Effortlessly stunning, just being yourself"
                imageSrc="/images/IMG_0929.jpeg"
              />
              <PictureDisplay
                title="Your radiant glow"
                description="The way you shine from within"
                imageSrc="/images/IMG_1311.jpeg"
              />
              <PictureDisplay
                title="Your gentle grace"
                description="Every movement like poetry in motion"
                imageSrc="/images/IMG_2039.jpeg"
              />
              <PictureDisplay
                title="Your sweet expression"
                description="The face that makes my heart skip beats"
                imageSrc="/images/IMG_2133.jpeg"
              />
              <PictureDisplay
                title="Your joyful spirit"
                description="The happiness you bring to my world"
                imageSrc="/images/IMG_2908.jpeg"
              />
              <PictureDisplay
                title="My everything"
                description="Perfect in every way, my heart's desire"
                imageSrc="/images/IMG_6711.jpeg"
              />
            </div>
          </div>
        </LetterSection>

        <LetterSection delay={1400}>
          <div>
            <h2 className="text-3xl font-light text-foreground mb-8 text-center">
              My Favorite Videos of You
            </h2>
            <p className="text-lg text-muted-foreground font-light leading-relaxed text-center mb-12">
              Your voice, your laugh, your spirit — captured in motion
            </p>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              <VideoDisplay
                title="Your beautiful laugh"
                description="The sound that makes my heart sing"
                videoSrc="/videos/IMG_0337.MP4"
              />
              <VideoDisplay
                title="You in motion"
                description="Every gesture, every movement captivates me"
                videoSrc="/videos/IMG_0728.MP4"
              />
              <VideoDisplay
                title="Your voice"
                description="The sweetest melody I've ever heard"
                videoSrc="/videos/IMG_0991.MP4"
              />
              <VideoDisplay
                title="Your playful side"
                description="The way you make everything fun and bright"
                videoSrc="/videos/IMG_2858.MP4"
              />
              <VideoDisplay
                title="Simply you"
                description="Perfect moments of you being beautifully you"
                videoSrc="/videos/IMG_2914.MP4"
              />
            </div>
          </div>
        </LetterSection>

        <LetterSection delay={1600}>
          <div className="text-center py-16">
            <p className="text-xl text-muted-foreground font-light leading-relaxed mb-8">
              What's all the playing around for?<br />
              Just tell me what you're down for.
            </p>
            <p className="text-2xl text-foreground font-light">
              Don't make it complicated
            </p>
            <div className="w-24 h-px bg-foreground/30 mx-auto mt-8"></div>
            <p className="text-lg text-muted-foreground font-light mt-8">
              You a Queen and I know...
            </p>

            <p>
              An angel told me, you gave Heaven its scent 
            </p>
          </div>
        </LetterSection>

        <LetterSection delay={1600}>
          <div className="border-t border-border/20 pt-12 text-center">
            <p className="text-lg text-foreground font-light">
              Hi, I'm boo... Nice to meet ya!
            </p>
            <p className="text-xl text-foreground font-medium mt-2">
              Your favorite person
            </p>
          </div>
        </LetterSection>

      </section>
    </div>
  );
};

export default Index;