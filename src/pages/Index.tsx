import { LetterSection } from "@/components/LetterSection";
import { VideoUpload } from "@/components/VideoUpload";
import { PictureUpload } from "@/components/PictureUpload";
import { FlashcardSection } from "@/components/FlashcardSection";
import heroBackground from "@/assets/hero-background.jpg";

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center">
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30"
          style={{ backgroundImage: `url(${heroBackground})` }}
        />
        <div className="relative z-10 text-center px-4 max-w-2xl mx-auto">
          <LetterSection>
            <p className="text-sm uppercase tracking-widest text-muted-foreground mb-8 font-sans">
              For My Love
            </p>
            <h1 className="text-5xl md:text-7xl font-light text-foreground mb-8 leading-tight">
              Black & White
            </h1>
            <p className="text-lg text-muted-foreground font-light leading-relaxed">
              It could be so simple — just you and me,<br />
              in perfect clarity
            </p>
          </LetterSection>
        </div>
      </section>

      {/* Letter Content */}
      <section className="max-w-4xl mx-auto px-6 py-20 space-y-16">
        
        <LetterSection delay={200}>
          <div className="prose prose-lg max-w-none">
            <p className="text-xl text-foreground font-light leading-relaxed mb-8">
              My Dearest,
            </p>
            <p className="text-lg text-muted-foreground font-light leading-relaxed">
              Let me in your temple. Show me what you're into. I want to know every corner 
              of your heart, every dream that lives behind your eyes. You come first, 
              like rent due — a priority that shapes everything else in my world.
            </p>
          </div>
        </LetterSection>

        <LetterSection delay={400}>
          <div className="border-l-2 border-foreground/20 pl-8">
            <p className="text-2xl text-foreground font-light leading-relaxed italic">
              "I see our potential"
            </p>
            <p className="text-lg text-muted-foreground font-light leading-relaxed mt-4">
              What we have doesn't need complications. No games, no pretense — 
              just this beautiful thing between us, as clear and honest as black and white.
            </p>
          </div>
        </LetterSection>

        <LetterSection delay={600}>
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-light text-foreground mb-6">
                My Promises
              </h2>
              <div className="space-y-6 text-muted-foreground">
                <p className="font-light leading-relaxed">
                  <span className="text-foreground font-medium">One:</span> Never give up on you, or me, or us.
                </p>
                <p className="font-light leading-relaxed">
                  <span className="text-foreground font-medium">Two:</span> Be patient with you like you're patient with me.
                </p>
                <p className="font-light leading-relaxed">
                  <span className="text-foreground font-medium">Three:</span> Help you see through those who pretend.
                </p>
                <p className="font-light leading-relaxed">
                  <span className="text-foreground font-medium">Four:</span> You already know — you're the one.
                </p>
              </div>
            </div>
            <div className="text-center">
              <div className="w-32 h-32 mx-auto border border-foreground/20 rounded-full flex items-center justify-center">
                <span className="text-4xl">∞</span>
              </div>
              <p className="text-sm text-muted-foreground mt-4 font-light">
                We the perfect two
              </p>
            </div>
          </div>
        </LetterSection>

        <LetterSection delay={800}>
          <div className="text-center py-12">
            <p className="text-3xl text-foreground font-light leading-relaxed">
              "I'll be your peace,<br />
              you be my baby"
            </p>
            <div className="w-16 h-px bg-foreground/30 mx-auto mt-8"></div>
          </div>
        </LetterSection>

        <LetterSection delay={1000}>
          <div className="bg-card rounded-lg p-8 border border-border/20">
            <p className="text-lg text-muted-foreground font-light leading-relaxed">
              I got a lot of love to give. I want you to call dibs on this heart 
              that beats for you. Can you hear it? Like any home, it comes with 
              promises — to never disappear, to be your bridge when you need to 
              cross over into something new.
            </p>
            <p className="text-lg text-foreground font-light leading-relaxed mt-6">
              You make heaven make sense.
            </p>
          </div>
        </LetterSection>

        <FlashcardSection />

        <LetterSection delay={1200}>
          <div>
            <h2 className="text-3xl font-light text-foreground mb-8 text-center">
              Our Pictures
            </h2>
            <p className="text-lg text-muted-foreground font-light leading-relaxed text-center mb-12">
              Captured moments that tell our story
            </p>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <PictureUpload
                title="When we first met"
                description="The moment I walked in the room and caught your view"
              />
              <PictureUpload
                title="Our favorite place"
                description="In your bed, on your stairs — when loving, we don't care"
              />
              <PictureUpload
                title="You being you"
                description="All mine, like wine — the way you carry yourself"
              />
              <PictureUpload
                title="Our adventures"
                description="Every journey we've taken together"
              />
              <PictureUpload
                title="Quiet moments"
                description="The simple times that mean everything"
              />
              <PictureUpload
                title="Our future"
                description="Bright as my teeth, long as bamboo"
              />
            </div>
          </div>
        </LetterSection>

        <LetterSection delay={1400}>
          <div>
            <h2 className="text-3xl font-light text-foreground mb-8 text-center">
              Our Videos
            </h2>
            <p className="text-lg text-muted-foreground font-light leading-relaxed text-center mb-12">
              Moving memories that bring us back
            </p>

            <div className="grid md:grid-cols-2 gap-8">
              <VideoUpload
                title="First dance"
                description="The way we moved like we'd done it a thousand times"
              />
              <VideoUpload
                title="Laughing together"
                description="Your laugh that makes everything better"
              />
              <VideoUpload
                title="Our song"
                description="The melody that plays when we're together"
              />
              <VideoUpload
                title="Future dreams"
                description="All the tomorrows we're building together"
              />
            </div>
          </div>
        </LetterSection>

        <LetterSection delay={1600}>
          <div className="text-center py-16">
            <p className="text-xl text-muted-foreground font-light leading-relaxed mb-8">
              What's all the playing around for?<br />
              Just tell me what you're down for.
            </p>
            <p className="text-2xl text-foreground font-light">
              Don't make it complicated
            </p>
            <div className="w-24 h-px bg-foreground/30 mx-auto mt-8"></div>
            <p className="text-lg text-muted-foreground font-light mt-8">
              Black and white, yeah
            </p>
          </div>
        </LetterSection>

        <LetterSection delay={1600}>
          <div className="border-t border-border/20 pt-12 text-center">
            <p className="text-lg text-foreground font-light">
              Forever yours,
            </p>
            <p className="text-xl text-foreground font-medium mt-2">
              Your favorite person
            </p>
          </div>
        </LetterSection>

      </section>
    </div>
  );
};

export default Index;