import { useState } from "react";
import { Upload, Play } from "lucide-react";

interface VideoUploadProps {
  title: string;
  description: string;
}

export const VideoUpload = ({ title, description }: VideoUploadProps) => {
  const [videoSrc, setVideoSrc] = useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const url = URL.createObjectURL(file);
      setVideoSrc(url);
    }
  };

  return (
    <div className="group">
      <h3 className="text-lg font-medium text-foreground mb-3 font-sans">
        {title}
      </h3>
      
      {videoSrc ? (
        <div className="relative">
          <video
            className="w-full rounded-lg shadow-soft"
            controls
            src={videoSrc}
          />
        </div>
      ) : (
        <label className="block cursor-pointer">
          <input
            type="file"
            accept="video/*"
            onChange={handleFileChange}
            className="hidden"
          />
          <div className="border-2 border-dashed border-border rounded-lg p-8 text-center hover:border-foreground/30 transition-colors">
            <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-3" />
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          </div>
        </label>
      )}
    </div>
  );
};