import { Heart } from "lucide-react";

interface PictureDisplayProps {
  title: string;
  description: string;
  imageSrc: string;
}

export const PictureDisplay = ({ title, description, imageSrc }: PictureDisplayProps) => {
  return (
    <div className="group">
      <h3 className="text-lg font-medium text-foreground mb-3 font-sans">
        {title}
      </h3>
      
      <div className="relative overflow-hidden rounded-lg shadow-soft">
        <img
          className="w-full object-cover aspect-square transition-transform duration-300 group-hover:scale-105"
          src={imageSrc}
          alt={title}
        />
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
          <Heart className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
      </div>
      
      <p className="text-sm text-muted-foreground mt-2 font-light">
        {description}
      </p>
    </div>
  );
};
