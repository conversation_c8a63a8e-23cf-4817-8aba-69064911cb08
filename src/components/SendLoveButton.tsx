import { useState } from "react";
import { Heart } from "lucide-react";

interface HeartParticle {
  id: number;
  x: number;
  y: number;
  size: number;
  color: string;
  duration: number;
  delay: number;
}

export const SendLoveButton = () => {
  const [hearts, setHearts] = useState<HeartParticle[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);

  const createHeartExplosion = () => {
    if (isAnimating) return;
    
    setIsAnimating(true);
    const newHearts: HeartParticle[] = [];
    
    // Create 15 hearts with random properties
    for (let i = 0; i < 15; i++) {
      newHearts.push({
        id: Date.now() + i,
        x: Math.random() * 400 - 200, // Random x position (-200 to 200)
        y: Math.random() * 400 - 200, // Random y position (-200 to 200)
        size: Math.random() * 20 + 10, // Random size (10-30px)
        color: `hsl(${Math.random() * 60 + 320}, 70%, ${Math.random() * 30 + 50}%)`, // Pink/red hues
        duration: Math.random() * 1000 + 1500, // Random duration (1.5-2.5s)
        delay: Math.random() * 200, // Random delay (0-200ms)
      });
    }
    
    setHearts(newHearts);
    
    // Clear hearts after animation
    setTimeout(() => {
      setHearts([]);
      setIsAnimating(false);
    }, 3000);
  };

  return (
    <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50">
      <div className="relative">
        {/* Heart particles */}
        {hearts.map((heart) => (
          <div
            key={heart.id}
            className="absolute pointer-events-none heart-explosion"
            style={{
              left: '50%',
              top: '50%',
              transform: `translate(-50%, -50%)`,
              animationDuration: `${heart.duration}ms`,
              animationDelay: `${heart.delay}ms`,
              '--heart-x': `${heart.x}px`,
              '--heart-y': `${heart.y}px`,
              '--heart-size': `${heart.size}px`,
              '--heart-color': heart.color,
            } as React.CSSProperties}
          >
            <Heart 
              className="heart-particle"
              style={{
                width: heart.size,
                height: heart.size,
                color: heart.color,
                fill: heart.color,
              }}
            />
          </div>
        ))}
        
        {/* Send Love Button */}
        <button
          onClick={createHeartExplosion}
          disabled={isAnimating}
          className={`
            group relative overflow-hidden
            bg-gradient-to-r from-pink-500 to-red-500 
            hover:from-pink-600 hover:to-red-600
            disabled:from-pink-400 disabled:to-red-400
            text-white font-medium px-8 py-4 rounded-full
            shadow-lg hover:shadow-xl
            transform transition-all duration-300
            hover:scale-105 active:scale-95
            disabled:cursor-not-allowed disabled:scale-100
            flex items-center gap-3
          `}
        >
          <Heart className={`w-5 h-5 transition-transform duration-300 ${isAnimating ? 'animate-pulse-heart' : 'group-hover:scale-110'}`} />
          <span className="text-lg font-light">
            {isAnimating ? 'Sending Love...' : 'Send Love'}
          </span>
          
          {/* Button glow effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-pink-400 to-red-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-full" />
        </button>
      </div>
    </div>
    </div>
  );
};
