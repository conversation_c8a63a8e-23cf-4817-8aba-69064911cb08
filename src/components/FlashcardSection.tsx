import { useState } from "react";
import { LetterSection } from "./LetterSection";

interface Flashcard {
  front: string;
  back: string;
}

const flashcards: Flashcard[] = [
  {
    front: "Mi amor, you're...",
    back: "the reason my heart knows how to dance"
  },
  {
    front: "<PERSON><PERSON><PERSON><PERSON>, your beautiful eyes...",
    back: "hold galaxies I want to explore forever"
  },
  {
    front: "Cariño, when you smile...",
    back: "the whole world makes sense to me"
  },
  {
    front: "Mi vida, your laugh...",
    back: "is the song I want to hear every morning"
  },
  {
    front: "Amor, your touch...",
    back: "feels like coming home to myself"
  },
  {
    front: "Ha<PERSON>bt<PERSON>, you are...",
    back: "my favorite dream that came true"
  }
];

export const FlashcardSection = () => {
  const [flippedCards, setFlippedCards] = useState<Set<number>>(new Set());

  const toggleCard = (index: number) => {
    console.log('Card clicked:', index); // Debug log
    const newFlippedCards = new Set(flippedCards);
    if (newFlippedCards.has(index)) {
      newFlippedCards.delete(index);
    } else {
      newFlippedCards.add(index);
    }
    console.log('New flipped cards:', newFlippedCards); // Debug log
    setFlippedCards(newFlippedCards);
  };

  return (
    <LetterSection delay={1000}>
      <div className="text-center mb-12">
        <h2 className="text-3xl font-light text-foreground mb-4">
          Sweet Words
        </h2>
        <p className="text-lg text-muted-foreground font-light">
          Click each card to reveal what's in my heart
        </p>
      </div>
      
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {flashcards.map((card, index) => (
          <div
            key={index}
            className="relative h-40 cursor-pointer"
            onClick={() => toggleCard(index)}
            style={{ perspective: '1000px' }}
          >
            <div
              className={`relative w-full h-full transition-transform duration-700 ${
                flippedCards.has(index) ? '[transform:rotateY(180deg)]' : ''
              }`}
              style={{
                transformStyle: 'preserve-3d',
              }}
            >
              {/* Front of card */}
              <div
                className="absolute inset-0 w-full h-full border border-border/20 rounded-lg bg-card flex items-center justify-center p-6"
                style={{ backfaceVisibility: 'hidden' }}
              >
                <p className="text-lg font-light text-foreground text-center leading-relaxed">
                  {card.front}
                </p>
              </div>

              {/* Back of card */}
              <div
                className="absolute inset-0 w-full h-full border border-border/20 rounded-lg bg-foreground text-background flex items-center justify-center p-6"
                style={{
                  backfaceVisibility: 'hidden',
                  transform: 'rotateY(180deg)'
                }}
              >
                <p className="text-lg font-light text-center leading-relaxed">
                  {card.back}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </LetterSection>
  );
};