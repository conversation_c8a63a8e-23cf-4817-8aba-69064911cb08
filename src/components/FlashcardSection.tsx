import { useState } from "react";
import { LetterSection } from "./LetterSection";

interface Flashcard {
  front: string;
  back: string;
}

const flashcards: Flashcard[] = [
  {
    front: "Mi amor, you're...",
    back: "the reason my heart knows how to dance"
  },
  {
    front: "<PERSON><PERSON><PERSON>, your beautiful eyes...",
    back: "hold galaxies I want to explore forever"
  },
  {
    front: "Cariño, when you smile...",
    back: "the whole world makes sense to me"
  },
  {
    front: "Mi vida, your laugh...",
    back: "is the song I want to hear every morning"
  },
  {
    front: "Amor, your touch...",
    back: "feels like coming home to myself"
  },
  {
    front: "<PERSON><PERSON><PERSON>, you are...",
    back: "my favorite dream that came true"
  }
];

export const FlashcardSection = () => {
  const [flippedCards, setFlippedCards] = useState<Set<number>>(new Set());

  const toggleCard = (index: number) => {
    const newFlippedCards = new Set(flippedCards);
    if (newFlippedCards.has(index)) {
      newFlippedCards.delete(index);
    } else {
      newFlippedCards.add(index);
    }
    setFlippedCards(newFlippedCards);
  };

  return (
    <LetterSection delay={1000}>
      <div className="text-center mb-12">
        <h2 className="text-3xl font-light text-foreground mb-4">
          Sweet Words
        </h2>
        <p className="text-lg text-muted-foreground font-light">
          Click each card to reveal what's in my heart
        </p>
      </div>
      
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {flashcards.map((card, index) => (
          <div
            key={index}
            className="relative h-40 cursor-pointer perspective-1000"
            onClick={() => toggleCard(index)}
          >
            <div
              className={`relative w-full h-full transition-transform duration-700 transform-style-preserve-3d ${
                flippedCards.has(index) ? 'rotate-y-180' : ''
              }`}
            >
              {/* Front of card */}
              <div className="absolute inset-0 w-full h-full backface-hidden border border-border/20 rounded-lg bg-card flex items-center justify-center p-6">
                <p className="text-lg font-light text-foreground text-center leading-relaxed">
                  {card.front}
                </p>
              </div>
              
              {/* Back of card */}
              <div className="absolute inset-0 w-full h-full backface-hidden rotate-y-180 border border-border/20 rounded-lg bg-foreground text-background flex items-center justify-center p-6">
                <p className="text-lg font-light text-center leading-relaxed">
                  {card.back}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </LetterSection>
  );
};