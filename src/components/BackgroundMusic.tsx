import { useState, useRef, useEffect } from "react";
import { Volume2, VolumeX } from "lucide-react";

export const BackgroundMusic = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(0.3);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
  }, [volume]);

  const toggleMusic = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play().catch(console.error);
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50 bg-card/80 backdrop-blur-sm border border-border/20 rounded-lg p-3 shadow-soft">
      <div className="flex items-center gap-3">
        <button
          onClick={toggleMusic}
          className="flex items-center justify-center w-8 h-8 rounded-full bg-foreground/10 hover:bg-foreground/20 transition-colors"
          aria-label={isPlaying ? "Pause music" : "Play music"}
        >
          {isPlaying ? (
            <Volume2 className="w-4 h-4 text-foreground" />
          ) : (
            <VolumeX className="w-4 h-4 text-foreground" />
          )}
        </button>
        
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={volume}
          onChange={handleVolumeChange}
          className="w-16 h-1 bg-border rounded-lg appearance-none cursor-pointer"
          style={{
            background: `linear-gradient(to right, hsl(var(--foreground)) 0%, hsl(var(--foreground)) ${volume * 100}%, hsl(var(--border)) ${volume * 100}%, hsl(var(--border)) 100%)`
          }}
        />
      </div>
      
      <audio
        ref={audioRef}
        loop
        preload="metadata"
      >
        <source src="/music/Nicki_Minaj_-_Needle_ft_Drake_Hitxclusive.co.mp3" type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>
    </div>
  );
};
