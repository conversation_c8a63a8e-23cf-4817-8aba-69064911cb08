import { Play, Heart } from "lucide-react";

interface VideoDisplayProps {
  title: string;
  description: string;
  videoSrc: string;
}

export const VideoDisplay = ({ title, description, videoSrc }: VideoDisplayProps) => {
  return (
    <div className="group">
      <h3 className="text-lg font-medium text-foreground mb-3 font-sans">
        {title}
      </h3>
      
      <div className="relative overflow-hidden rounded-lg shadow-soft">
        <video
          className="w-full rounded-lg transition-transform duration-300 group-hover:scale-105"
          controls
          src={videoSrc}
          preload="metadata"
        />
        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Heart className="w-5 h-5 text-white drop-shadow-lg" />
        </div>
      </div>
      
      <p className="text-sm text-muted-foreground mt-2 font-light">
        {description}
      </p>
    </div>
  );
};
