import { useState } from "react";
import { Upload, Image } from "lucide-react";

interface PictureUploadProps {
  title: string;
  description: string;
}

export const PictureUpload = ({ title, description }: PictureUploadProps) => {
  const [imageSrc, setImageSrc] = useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const url = URL.createObjectURL(file);
      setImageSrc(url);
    }
  };

  return (
    <div className="group">
      <h3 className="text-lg font-medium text-foreground mb-3 font-sans">
        {title}
      </h3>
      
      {imageSrc ? (
        <div className="relative">
          <img
            className="w-full rounded-lg shadow-soft object-cover aspect-square"
            src={imageSrc}
            alt={title}
          />
        </div>
      ) : (
        <label className="block cursor-pointer">
          <input
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
          />
          <div className="border-2 border-dashed border-border rounded-lg p-8 text-center hover:border-foreground/30 transition-colors aspect-square flex flex-col items-center justify-center">
            <Image className="w-8 h-8 text-muted-foreground mx-auto mb-3" />
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          </div>
        </label>
      )}
    </div>
  );
};
